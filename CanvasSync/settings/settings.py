"""
CanvasSync by <PERSON>
February 2017

--------------------------------------------

settings.py, Class

The Settings object implements the functionality of setting the initial-launch
settings and later loading these settings
These settings include:

1) A sync path provided via command line where synchronization will occur and
   where the settings file will be stored.
2) The domain of the Canvas web server.
3) An authentication token used to authenticate with the Canvas API. The token
   is generated on the Canvas web server
   after authentication under "Settings".

The Settings object will prompt the user for these settings through the
set_settings method and write them to a JSON file (.canvas_sync.json) in the
sync path directory. Settings are stored in plain text JSON format.
"""

# TODO
# - Clean things
# - Implement ANKI.fomrat method instead of accessing the ANKI attributes
#   directly
# - Make it possible reuse settings, so that you do not have to
#   re-specify all settings to change a single one

# Future imports
from __future__ import print_function

# Inbuilt modules
import os
import sys
import json

# Third party modules
from six.moves import input

# CanvasSync modules
from CanvasSync.settings import user_prompter
from CanvasSync.utilities.instructure_api import InstructureApi
from CanvasSync.utilities.ANSI import ANSI
from CanvasSync.utilities import helpers


class Settings(object):
    def __init__(self, sync_path=None):
        self.sync_path = sync_path or u"Not set"
        self.domain = u"Not set"
        self.token = u"Not set"
        self.courses_to_sync = [u"Not set"]
        self.modules_settings = {u"Files": True,
                                 u"HTML pages": True,
                                 u"External URLs": True}
        self.sync_assignments = True
        self.download_linked = True
        self.avoid_duplicates = True
        self.use_nicknames = False

        # Get the path pointing to the settings file.
        if self.sync_path != u"Not set":
            self.settings_path = os.path.join(self.sync_path, u".canvas_sync.json")
        else:
            self.settings_path = None

        # Initialize user prompt class, used to get information from the user
        # via the terminal
        self.api = InstructureApi(self)

    def settings_file_exists(self):
        """
        Returns a boolean representing if the settings file
        has already been created in the sync path
        """
        return self.settings_path and os.path.exists(self.settings_path)

    def is_loaded(self):
        return self.sync_path != u"Not set" and \
               self.domain != u"Not set" and \
               self.token != u"Not set" and \
               self.courses_to_sync[0] != u"Not set"

    def load_settings(self, password=None):
        """
        Loads the current settings from the settings file and sets the
        attributes of the Settings object
        """
        if self.is_loaded():
            return helpers.validate_token(self.domain, self.token)

        if self.sync_path == u"Not set":
            print(ANSI.format(u"\n[ERROR] No sync path specified. Please use -d/--sync-path to specify a sync directory.", u"announcer"))
            return False

        if not self.settings_file_exists():
            self.set_settings()
            return True

        try:
            with open(self.settings_path, u"r", encoding="utf-8") as settings_f:
                settings_data = json.load(settings_f)

            # Load settings from JSON
            self.domain = settings_data.get(u"domain", u"Not set")
            self.token = settings_data.get(u"token", u"Not set")
            self.courses_to_sync = settings_data.get(u"courses_to_sync", [u"Not set"])
            self.modules_settings = settings_data.get(u"modules_settings", {
                u"Files": True,
                u"HTML pages": True,
                u"External URLs": True
            })
            self.sync_assignments = settings_data.get(u"sync_assignments", True)
            self.download_linked = settings_data.get(u"download_linked", True)
            self.avoid_duplicates = settings_data.get(u"avoid_duplicates", True)
            self.use_nicknames = settings_data.get(u"use_nicknames", False)

        except (IOError, ValueError, json.JSONDecodeError) as e:
            print(ANSI.format(u"\n[ERROR] Could not load settings file: %s" % str(e), u"announcer"))
            print(ANSI.format(u"        Please run with -s/--setup to reconfigure.", u"announcer"))
            return False

        if not helpers.validate_token(self.domain, self.token):
            return False
        else:
            return True

    def set_settings(self):
        try:
            self._set_settings()
        except KeyboardInterrupt:
            print(ANSI.format(u"\n\n[*] Setup interrupted, nothing was saved.", formatting=u"red"))
            sys.exit()

        self.write_settings()

    def _set_settings(self):
        """
        Prompt the user for settings and write the information to the sync path as JSON.
        """

        if self.sync_path == u"Not set":
            print(ANSI.format(u"\n[ERROR] No sync path specified. Please use -d/--sync-path to specify a sync directory.", u"announcer"))
            return

        # Clear the console and print guidance
        self.print_settings(first_time_setup=True, clear=True)

        # Prompt user for domain
        self.domain = user_prompter.ask_for_domain()
        self.print_settings(first_time_setup=True, clear=True)

        # Prompt user for auth token
        self.token = user_prompter.ask_for_token(domain=self.domain)
        self.print_settings(first_time_setup=True, clear=True)

        # Prompt user for course sync selection
        self.courses_to_sync = user_prompter.ask_for_courses(self, api=self.api)
        self.print_settings(first_time_setup=True, clear=True)

        # Ask user for advanced settings
        show_advanced = user_prompter.ask_for_advanced_settings(self)

        if show_advanced:
            self.modules_settings = user_prompter.ask_for_module_settings(self.modules_settings, self)
            self.sync_assignments = user_prompter.ask_for_assignment_sync(self)
            if not self.sync_assignments:
                self.download_linked = False
            else:
                self.download_linked = user_prompter.ask_for_download_linked(self)
            self.avoid_duplicates = user_prompter.ask_for_avoid_duplicates(self)

    def write_settings(self):
        self.print_settings(first_time_setup=False, clear=True)
        self.print_advanced_settings(clear=False)
        print(ANSI.format(u"\n\nThese settings will be saved to: %s" % self.settings_path, u"announcer"))

        # Create settings dictionary
        settings_data = {
            u"domain": self.domain,
            u"token": self.token,
            u"courses_to_sync": self.courses_to_sync,
            u"modules_settings": self.modules_settings,
            u"sync_assignments": self.sync_assignments,
            u"download_linked": self.download_linked,
            u"avoid_duplicates": self.avoid_duplicates,
            u"use_nicknames": self.use_nicknames
        }

        # Write settings as JSON to the sync path
        try:
            with open(self.settings_path, u"w", encoding="utf-8") as out_file:
                json.dump(settings_data, out_file, indent=2, ensure_ascii=False)
            print(ANSI.format(u"Settings saved successfully!", u"announcer"))
        except IOError as e:
            print(ANSI.format(u"\n[ERROR] Could not write settings file: %s" % str(e), u"announcer"))

    def print_advanced_settings(self, clear=True):
        """
        Print the advanced settings currently in memory.
        Clear the console first if specified by the 'clear' parameter
        """
        if clear:
            helpers.clear_console()

        print(ANSI.format(u"\nAdvanced settings", u"announcer"))

        module_settings_string = ANSI.BOLD + u"[*] Sync module items:        \t" + ANSI.ENDC

        count = 0
        for item in self.modules_settings:
            if self.modules_settings[item]:
                d = u" & " if count != 0 else u""
                module_settings_string += d + ANSI.BLUE + item + ANSI.ENDC
                count += 1

        if count == 0:
            module_settings_string += ANSI.RED + u"False" + ANSI.ENDC

        print(module_settings_string)
        print(ANSI.BOLD + u"[*] Sync assignments:         \t" + ANSI.ENDC + (ANSI.GREEN if self.sync_assignments else ANSI.RED) + str(self.sync_assignments) + ANSI.ENDC)
        print(ANSI.BOLD + u"[*] Download linked files:    \t" + ANSI.ENDC + (ANSI.GREEN if self.download_linked else ANSI.RED) + str(self.download_linked) + ANSI.ENDC)
        print(ANSI.BOLD + u"[*] Avoid item duplicates:    \t" + ANSI.ENDC + (ANSI.GREEN if self.avoid_duplicates else ANSI.RED) + str(self.avoid_duplicates) + ANSI.ENDC)

    def print_settings(self, first_time_setup=True, clear=True):
        """
        Print the settings currently in memory.
        Clear the console first if specified by the 'clear' parameter
        """
        if clear:
            helpers.clear_console()

        if first_time_setup:
            print(ANSI.format(u"This is a first time setup.\nYou must specify "
                              u"at least the following settings"
                              u" in order to run CanvasSync:\n", u"announcer"))
        else:
            print(ANSI.format(u"-----------------------------", u"file"))
            print(ANSI.format(u"CanvasSync - Current settings", u"file"))
            print(ANSI.format(u"-----------------------------\n", u"file"))
            print(ANSI.format(u"Standard settings", u"announcer"))

        print(ANSI.BOLD + u"[*] Sync path:             \t" + ANSI.ENDC + ANSI.BLUE + self.sync_path + ANSI.ENDC)
        print(ANSI.BOLD + u"[*] Canvas domain:         \t" + ANSI.ENDC + ANSI.BLUE + self.domain + ANSI.ENDC)
        print(ANSI.BOLD + u"[*] Authentication token:  \t" + ANSI.ENDC + ANSI.BLUE + self.token + ANSI.ENDC)

        if len(self.courses_to_sync) != 0:
            if self.courses_to_sync[0] == u"Not set":
                d = u""
            else:
                d = u"1) "
            print(ANSI.BOLD + u"[*] Courses to be synced:  \t%s" % d + ANSI.ENDC + ANSI.BLUE + self.courses_to_sync[0] + ANSI.ENDC)

            for index, course in enumerate(self.courses_to_sync[1:]):
                print(u" "*27 + u"\t%s) " % (index+2) + ANSI.BLUE + course + ANSI.ENDC)

    def show(self, quit=True):
        """
        Show the current settings
        If quit=True, sys.exit after user confirmation
        """
        valid_token = self.load_settings()

        self.print_settings(first_time_setup=False, clear=True)
        self.print_advanced_settings(clear=False)

        if not valid_token:
            self.print_auth_token_reset_error()

        if quit:
            sys.exit()
        else:
            input(u"\nHit enter to continue.")

    def print_auth_token_reset_error(self):
        """
        Prints error message for when the auth token stored in the
        settings is no longer valid
        """
        print(u"\n\n[ERROR] The authentication token has been reset.\n"
              u"        You must generate a new from the canvas webpage and"
              u" reset the CanvasSync settings\n"
              u"        using the --setup command line arguments or from the"
              u" main menu.")

    def show_main_screen(self, settings_file_exists):
        """
        Linker method to the show_main_screen function of the
        user_prompter module
        """
        return user_prompter.show_main_screen(settings_file_exists)
